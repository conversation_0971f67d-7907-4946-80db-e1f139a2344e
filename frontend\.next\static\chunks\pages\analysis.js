/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/analysis"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/analysis\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/analysis.tsx */ \"./src/pages/analysis.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/analysis\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDYWktcHJvZHVjdC1zZWxlY3QlNUNmcm9udGVuZCU1Q3NyYyU1Q3BhZ2VzJTVDYW5hbHlzaXMudHN4JnBhZ2U9JTJGYW5hbHlzaXMhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMERBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz83ZjgxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvYW5hbHlzaXNcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9hbmFseXNpcy50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2FuYWx5c2lzXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis!\n"));

/***/ }),

/***/ "./src/pages/analysis.tsx":
/*!********************************!*\
  !*** ./src/pages/analysis.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst AnalysisPage = ()=>{\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            message: \"加载智能分析...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n            lineNumber: 10,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    // 模拟分析报告数据\n    const reports = [\n        {\n            id: 1,\n            title: \"iPhone 15 Pro Max 市场分析报告\",\n            category: \"手机\",\n            status: \"已完成\",\n            trend: \"up\",\n            score: 85,\n            createdAt: \"2024-01-15\",\n            summary: \"该产品在高端手机市场表现优异，建议加大库存投入\",\n            metrics: {\n                marketShare: \"15.2%\",\n                profitMargin: \"32%\",\n                competitorCount: 8\n            }\n        },\n        {\n            id: 2,\n            title: \"小米14 Ultra 竞品分析\",\n            category: \"手机\",\n            status: \"已完成\",\n            trend: \"up\",\n            score: 78,\n            createdAt: \"2024-01-12\",\n            summary: \"性价比优势明显，在中高端市场有较强竞争力\",\n            metrics: {\n                marketShare: \"12.8%\",\n                profitMargin: \"28%\",\n                competitorCount: 12\n            }\n        },\n        {\n            id: 3,\n            title: \"MacBook Pro M3 销售预测\",\n            category: \"笔记本\",\n            status: \"进行中\",\n            trend: \"up\",\n            score: 92,\n            createdAt: \"2024-01-10\",\n            summary: \"预计Q2销量将增长25%，建议提前备货\",\n            metrics: {\n                marketShare: \"8.5%\",\n                profitMargin: \"35%\",\n                competitorCount: 6\n            }\n        }\n    ];\n    const categories = [\n        \"all\",\n        \"手机\",\n        \"笔记本\",\n        \"耳机\"\n    ];\n    const filteredReports = selectedCategory === \"all\" ? reports : reports.filter((report)=>report.category === selectedCategory);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n            padding: \"2rem 0\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: \"1200px\",\n                margin: \"0 auto\",\n                padding: \"0 1rem\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    style: {\n                        marginBottom: \"2rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                justifyContent: \"space-between\",\n                                alignItems: \"center\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            style: {\n                                                fontSize: \"2rem\",\n                                                fontWeight: \"700\",\n                                                color: \"var(--color-gray-900)\",\n                                                marginBottom: \"0.5rem\"\n                                            },\n                                            children: \"智能分析\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                fontSize: \"1rem\",\n                                                color: \"var(--color-gray-600)\"\n                                            },\n                                            children: \"AI驱动的产品市场分析和预测\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn btn-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                width: \"1.25rem\",\n                                                height: \"1.25rem\"\n                                            },\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"新建分析\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"grid\",\n                        gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                        gap: \"1.5rem\",\n                        marginBottom: \"2rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"3rem\",\n                                                height: \"3rem\",\n                                                background: \"linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))\",\n                                                borderRadius: \"var(--radius-lg)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    width: \"1.5rem\",\n                                                    height: \"1.5rem\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"2rem\",\n                                                        fontWeight: \"700\",\n                                                        color: \"var(--color-gray-900)\"\n                                                    },\n                                                    children: reports.length\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"0.875rem\",\n                                                        color: \"var(--color-gray-600)\"\n                                                    },\n                                                    children: \"分析报告\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"3rem\",\n                                                height: \"3rem\",\n                                                background: \"linear-gradient(135deg, var(--color-success-500), var(--color-success-600))\",\n                                                borderRadius: \"var(--radius-lg)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    width: \"1.5rem\",\n                                                    height: \"1.5rem\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"2rem\",\n                                                        fontWeight: \"700\",\n                                                        color: \"var(--color-gray-900)\"\n                                                    },\n                                                    children: \"82%\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"0.875rem\",\n                                                        color: \"var(--color-gray-600)\"\n                                                    },\n                                                    children: \"平均准确率\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"card-body\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"3rem\",\n                                                height: \"3rem\",\n                                                background: \"linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600))\",\n                                                borderRadius: \"var(--radius-lg)\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    width: \"1.5rem\",\n                                                    height: \"1.5rem\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"2rem\",\n                                                        fontWeight: \"700\",\n                                                        color: \"var(--color-gray-900)\"\n                                                    },\n                                                    children: \"15\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        fontSize: \"0.875rem\",\n                                                        color: \"var(--color-gray-600)\"\n                                                    },\n                                                    children: \"AI模型\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    style: {\n                        marginBottom: \"2rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: \"flex\",\n                                gap: \"0.5rem\"\n                            },\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"btn \".concat(selectedCategory === category ? \"btn-primary\" : \"btn-ghost\", \" btn-sm\"),\n                                    onClick: ()=>setSelectedCategory(category),\n                                    children: category === \"all\" ? \"全部\" : category\n                                }, category, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card-body\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    flexDirection: \"column\",\n                                    gap: \"1rem\"\n                                },\n                                children: filteredReports.map((report)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"flex-start\",\n                                                        marginBottom: \"1rem\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                flex: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        alignItems: \"center\",\n                                                                        gap: \"1rem\",\n                                                                        marginBottom: \"0.5rem\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            style: {\n                                                                                fontSize: \"1.25rem\",\n                                                                                fontWeight: \"600\",\n                                                                                color: \"var(--color-gray-900)\"\n                                                                            },\n                                                                            children: report.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 240,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"badge \".concat(report.status === \"已完成\" ? \"badge-success\" : \"badge-warning\"),\n                                                                            children: report.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            style: {\n                                                                                width: \"1.25rem\",\n                                                                                height: \"1.25rem\",\n                                                                                color: report.trend === \"up\" ? \"var(--color-success-500)\" : \"var(--color-error-500)\"\n                                                                            },\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                lineNumber: 255,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    style: {\n                                                                        fontSize: \"0.875rem\",\n                                                                        color: \"var(--color-gray-600)\",\n                                                                        marginBottom: \"1rem\"\n                                                                    },\n                                                                    children: report.summary\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: \"grid\",\n                                                                        gridTemplateColumns: \"repeat(auto-fit, minmax(150px, 1fr))\",\n                                                                        gap: \"1rem\",\n                                                                        marginBottom: \"1rem\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        color: \"var(--color-gray-500)\"\n                                                                                    },\n                                                                                    children: \"市场份额\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    style: {\n                                                                                        fontSize: \"1rem\",\n                                                                                        fontWeight: \"600\",\n                                                                                        color: \"var(--color-gray-900)\"\n                                                                                    },\n                                                                                    children: report.metrics.marketShare\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 275,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        color: \"var(--color-gray-500)\"\n                                                                                    },\n                                                                                    children: \"利润率\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 280,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    style: {\n                                                                                        fontSize: \"1rem\",\n                                                                                        fontWeight: \"600\",\n                                                                                        color: \"var(--color-gray-900)\"\n                                                                                    },\n                                                                                    children: report.metrics.profitMargin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 279,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    style: {\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        color: \"var(--color-gray-500)\"\n                                                                                    },\n                                                                                    children: \"竞品数量\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 286,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    style: {\n                                                                                        fontSize: \"1rem\",\n                                                                                        fontWeight: \"600\",\n                                                                                        color: \"var(--color-gray-900)\"\n                                                                                    },\n                                                                                    children: report.metrics.competitorCount\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 287,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 285,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                gap: \"1rem\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    textAlign: \"center\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: \"1.5rem\",\n                                                                            fontWeight: \"700\",\n                                                                            color: report.score >= 80 ? \"var(--color-success-600)\" : report.score >= 60 ? \"var(--color-warning-600)\" : \"var(--color-error-600)\"\n                                                                        },\n                                                                        children: report.score\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: \"0.75rem\",\n                                                                            color: \"var(--color-gray-500)\"\n                                                                        },\n                                                                        children: \"分析评分\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        justifyContent: \"space-between\",\n                                                        alignItems: \"center\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                fontSize: \"0.875rem\",\n                                                                color: \"var(--color-gray-500)\"\n                                                            },\n                                                            children: [\n                                                                \"创建时间: \",\n                                                                report.createdAt\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: \"flex\",\n                                                                gap: \"0.5rem\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"btn btn-ghost btn-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            style: {\n                                                                                width: \"1rem\",\n                                                                                height: \"1rem\"\n                                                                            },\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                    strokeLinecap: \"round\",\n                                                                                    strokeLinejoin: \"round\",\n                                                                                    strokeWidth: 2,\n                                                                                    d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                    lineNumber: 328,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"查看详情\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"btn btn-ghost btn-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            style: {\n                                                                                width: \"1rem\",\n                                                                                height: \"1rem\"\n                                                                            },\n                                                                            fill: \"none\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            stroke: \"currentColor\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                                lineNumber: 334,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"导出报告\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                                    lineNumber: 332,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, report.id, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 13\n                            }, undefined),\n                            filteredReports.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: \"center\",\n                                    padding: \"3rem\",\n                                    color: \"var(--color-gray-500)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"暂无分析报告\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\analysis.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnalysisPage, \"Js/Fxi04VewjqzAAlAJHljwr7qg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth\n    ];\n});\n_c = AnalysisPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnalysisPage);\nvar _c;\n$RefreshReg$(_c, \"AnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/analysis.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Canalysis.tsx&page=%2Fanalysis!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);