import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  TrashIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import { useNotify } from '@/contexts/NotificationContext';
import apiService from '@/services/api';
import { LoadingSpinner, TableSkeleton } from '@/components/LoadingScreen';

interface ImportTask {
  id: string;
  title: string;
  file_name: string;
  file_size: number;
  file_type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  success_rows: number;
  failed_rows: number;
  total_rows: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const ImportTasksPage: React.FC = () => {
  useRequireAuth();
  const router = useRouter();
  const notify = useNotify();
  
  const [tasks, setTasks] = useState<ImportTask[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [actionLoading, setActionLoading] = useState<string>('');

  // 加载任务列表
  const loadTasks = async (page = 1, status = '') => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });
      
      if (status) {
        params.append('status', status);
      }
      
      const response = await apiService.get(`/import/tasks?${params}`);
      setTasks(response.tasks || []);
      setPagination(response.pagination);
    } catch (error) {
      console.error('加载任务列表失败:', error);
      notify.error('加载失败', '无法加载导入任务列表');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadTasks(1, statusFilter);
  }, [statusFilter]);

  // 重试任务
  const retryTask = async (taskId: string) => {
    setActionLoading(taskId);
    try {
      await apiService.post(`/import/tasks/${taskId}/retry`);
      notify.success('重试成功', '任务已重新启动');
      loadTasks(pagination.page, statusFilter);
    } catch (error: any) {
      notify.error('重试失败', error.message || '重试任务失败');
    } finally {
      setActionLoading('');
    }
  };

  // 删除任务
  const deleteTask = async (taskId: string) => {
    if (!confirm('确定要删除这个导入任务吗？')) {
      return;
    }
    
    setActionLoading(taskId);
    try {
      await apiService.delete(`/import/tasks/${taskId}`);
      notify.success('删除成功', '导入任务已删除');
      loadTasks(pagination.page, statusFilter);
    } catch (error: any) {
      notify.error('删除失败', error.message || '删除任务失败');
    } finally {
      setActionLoading('');
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'processing':
        return <LoadingSpinner size="sm" className="text-blue-500" />;
      default:
        return <ClockIcon className="w-5 h-5 text-yellow-500" />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const statusMap = {
      pending: '等待处理',
      processing: '处理中',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">导入任务</h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            管理您的数据导入任务
          </p>
        </div>
        <button
          onClick={() => router.push('/import')}
          className="btn btn-primary"
        >
          新建导入
        </button>
      </div>

      {/* 筛选器 */}
      <div className="card">
        <div className="card-body">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              状态筛选：
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="input w-auto"
            >
              <option value="">全部状态</option>
              <option value="pending">等待处理</option>
              <option value="processing">处理中</option>
              <option value="completed">已完成</option>
              <option value="failed">失败</option>
            </select>
          </div>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="card">
        <div className="card-body p-0">
          {isLoading ? (
            <div className="p-6">
              <TableSkeleton rows={5} columns={6} />
            </div>
          ) : tasks.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">文件名</th>
                    <th className="table-header-cell">状态</th>
                    <th className="table-header-cell">文件大小</th>
                    <th className="table-header-cell">导入结果</th>
                    <th className="table-header-cell">创建时间</th>
                    <th className="table-header-cell">操作</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {tasks.map((task) => (
                    <tr key={task.id}>
                      <td className="table-cell">
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {task.file_name}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {task.file_type.toUpperCase()}
                          </p>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(task.status)}
                          <span className="text-sm">
                            {getStatusText(task.status)}
                          </span>
                        </div>
                      </td>
                      <td className="table-cell">
                        {formatFileSize(task.file_size)}
                      </td>
                      <td className="table-cell">
                        {task.status === 'completed' ? (
                          <div className="text-sm">
                            <span className="text-green-600 dark:text-green-400">
                              成功: {task.success_rows}
                            </span>
                            {task.failed_rows > 0 && (
                              <>
                                <br />
                                <span className="text-red-500">
                                  失败: {task.failed_rows}
                                </span>
                              </>
                            )}
                          </div>
                        ) : task.status === 'failed' ? (
                          <span className="text-sm text-red-500">
                            导入失败
                          </span>
                        ) : (
                          <span className="text-sm text-gray-500">
                            -
                          </span>
                        )}
                      </td>
                      <td className="table-cell">
                        <div className="text-sm">
                          <div>{new Date(task.created_at).toLocaleDateString('zh-CN')}</div>
                          <div className="text-gray-500">
                            {new Date(task.created_at).toLocaleTimeString('zh-CN')}
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => router.push(`/import/tasks/${task.id}`)}
                            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            title="查看详情"
                          >
                            <EyeIcon className="w-4 h-4" />
                          </button>
                          
                          {task.status === 'failed' && (
                            <button
                              onClick={() => retryTask(task.id)}
                              disabled={actionLoading === task.id}
                              className="p-1 text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 disabled:opacity-50"
                              title="重试"
                            >
                              {actionLoading === task.id ? (
                                <LoadingSpinner size="sm" />
                              ) : (
                                <ArrowPathIcon className="w-4 h-4" />
                              )}
                            </button>
                          )}
                          
                          <button
                            onClick={() => deleteTask(task.id)}
                            disabled={actionLoading === task.id}
                            className="p-1 text-red-400 hover:text-red-600 dark:hover:text-red-300 disabled:opacity-50"
                            title="删除"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <ClockIcon className="w-8 h-8 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                {statusFilter ? '没有找到符合条件的导入任务' : '暂无导入任务'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 分页 */}
      {pagination.pages > 1 && (
        <div className="flex justify-center">
          <nav className="flex items-center space-x-2">
            <button
              onClick={() => loadTasks(pagination.page - 1, statusFilter)}
              disabled={pagination.page <= 1}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            
            <span className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300">
              第 {pagination.page} 页，共 {pagination.pages} 页
            </span>
            
            <button
              onClick={() => loadTasks(pagination.page + 1, statusFilter)}
              disabled={pagination.page >= pagination.pages}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};

export default ImportTasksPage;
