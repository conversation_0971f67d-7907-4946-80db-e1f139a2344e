import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';

const InsightsPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');

  if (isLoading) {
    return <LoadingScreen message="加载市场洞察..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 模拟市场数据
  const marketData = {
    '7d': {
      trends: [
        { category: '手机', value: 85, change: '+12.5%', color: '#3b82f6' },
        { category: '笔记本', value: 72, change: '-3.2%', color: '#ef4444' },
        { category: '耳机', value: 91, change: '+25.8%', color: '#10b981' },
        { category: '平板', value: 68, change: '****%', color: '#f59e0b' },
      ],
      priceData: [
        { name: '周一', iPhone: 9999, 小米: 3999, 华为: 5999 },
        { name: '周二', iPhone: 9899, 小米: 3899, 华为: 5899 },
        { name: '周三', iPhone: 9799, 小米: 3799, 华为: 5799 },
        { name: '周四', iPhone: 9699, 小米: 3699, 华为: 5699 },
        { name: '周五', iPhone: 9599, 小米: 3599, 华为: 5599 },
        { name: '周六', iPhone: 9499, 小米: 3499, 华为: 5499 },
        { name: '周日', iPhone: 9399, 小米: 3399, 华为: 5399 },
      ]
    }
  };

  const currentData = marketData[selectedTimeRange as keyof typeof marketData];

  // 简单的柱状图组件
  const BarChart = ({ data }: { data: any[] }) => {
    const maxValue = Math.max(...data.map(d => d.value));
    
    return (
      <div style={{ padding: '1rem' }}>
        <div style={{ display: 'flex', alignItems: 'end', gap: '1rem', height: '200px' }}>
          {data.map((item, index) => (
            <div key={index} style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center',
              flex: 1
            }}>
              <div style={{
                width: '100%',
                maxWidth: '60px',
                height: `${(item.value / maxValue) * 160}px`,
                backgroundColor: item.color,
                borderRadius: '4px 4px 0 0',
                marginBottom: '0.5rem',
                position: 'relative',
                transition: 'all 0.3s ease'
              }}>
                <div style={{
                  position: 'absolute',
                  top: '-1.5rem',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  color: 'var(--color-gray-700)'
                }}>
                  {item.value}
                </div>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '0.875rem', fontWeight: '500', marginBottom: '0.25rem' }}>
                  {item.category}
                </div>
                <div style={{ 
                  fontSize: '0.75rem', 
                  color: item.change.startsWith('+') ? 'var(--color-success-600)' : 'var(--color-error-600)',
                  fontWeight: '500'
                }}>
                  {item.change}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 简单的折线图组件
  const LineChart = ({ data }: { data: any[] }) => {
    const brands = ['iPhone', '小米', '华为'];
    const colors = ['#3b82f6', '#10b981', '#f59e0b'];
    
    return (
      <div style={{ padding: '1rem' }}>
        <div style={{ position: 'relative', height: '200px', background: 'var(--color-gray-50)', borderRadius: '0.5rem', padding: '1rem' }}>
          {/* Y轴标签 */}
          <div style={{ position: 'absolute', left: '0', top: '0', bottom: '0', width: '3rem', display: 'flex', flexDirection: 'column', justifyContent: 'space-between', fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>
            <span>10000</span>
            <span>7500</span>
            <span>5000</span>
            <span>2500</span>
            <span>0</span>
          </div>
          
          {/* 图表区域 */}
          <div style={{ marginLeft: '3rem', height: '100%', position: 'relative' }}>
            {/* 网格线 */}
            {[0, 25, 50, 75, 100].map(percent => (
              <div key={percent} style={{
                position: 'absolute',
                top: `${percent}%`,
                left: 0,
                right: 0,
                height: '1px',
                background: 'var(--color-gray-200)'
              }} />
            ))}
            
            {/* 数据线 */}
            {brands.map((brand, brandIndex) => (
              <svg key={brand} style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
                <polyline
                  fill="none"
                  stroke={colors[brandIndex]}
                  strokeWidth="2"
                  points={data.map((item, index) => {
                    const x = (index / (data.length - 1)) * 100;
                    const y = 100 - ((item[brand] / 10000) * 100);
                    return `${x}%,${y}%`;
                  }).join(' ')}
                />
                {data.map((item, index) => {
                  const x = (index / (data.length - 1)) * 100;
                  const y = 100 - ((item[brand] / 10000) * 100);
                  return (
                    <circle
                      key={index}
                      cx={`${x}%`}
                      cy={`${y}%`}
                      r="3"
                      fill={colors[brandIndex]}
                    />
                  );
                })}
              </svg>
            ))}
          </div>
          
          {/* X轴标签 */}
          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '0.5rem', marginLeft: '3rem', fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>
            {data.map(item => (
              <span key={item.name}>{item.name}</span>
            ))}
          </div>
        </div>
        
        {/* 图例 */}
        <div style={{ display: 'flex', justifyContent: 'center', gap: '1rem', marginTop: '1rem' }}>
          {brands.map((brand, index) => (
            <div key={brand} style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <div style={{ width: '12px', height: '12px', backgroundColor: colors[index], borderRadius: '2px' }} />
              <span style={{ fontSize: '0.875rem', color: 'var(--color-gray-700)' }}>{brand}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  市场洞察
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  实时市场趋势分析和商业洞察
                </p>
              </div>
              <div style={{ display: 'flex', gap: '0.5rem' }}>
                {['7d', '30d', '90d'].map((range) => (
                  <button
                    key={range}
                    className={`btn ${selectedTimeRange === range ? 'btn-primary' : 'btn-ghost'} btn-sm`}
                    onClick={() => setSelectedTimeRange(range)}
                  >
                    {range === '7d' ? '7天' : range === '30d' ? '30天' : '90天'}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 统计概览 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    15
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    市场洞察
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-success-500), var(--color-success-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    82%
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    准确率
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    8
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    机会识别
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 图表区域 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(500px, 1fr))',
          gap: '2rem',
          marginBottom: '2rem'
        }}>
          {/* 市场趋势柱状图 */}
          <div className="card">
            <div className="card-header">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: 'var(--color-gray-900)',
                margin: 0
              }}>
                市场趋势指数
              </h2>
            </div>
            <div className="card-body">
              <BarChart data={currentData.trends} />
            </div>
          </div>

          {/* 价格趋势折线图 */}
          <div className="card">
            <div className="card-header">
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: 'var(--color-gray-900)',
                margin: 0
              }}>
                价格趋势分析
              </h2>
            </div>
            <div className="card-body">
              <LineChart data={currentData.priceData} />
            </div>
          </div>
        </div>

        {/* 洞察建议 */}
        <div className="card">
          <div className="card-header">
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: 'var(--color-gray-900)',
              margin: 0
            }}>
              AI智能建议
            </h2>
          </div>
          <div className="card-body">
            <div style={{ display: 'grid', gap: '1rem' }}>
              <div style={{
                padding: '1rem',
                background: 'var(--color-success-50)',
                border: '1px solid var(--color-success-200)',
                borderRadius: 'var(--radius-lg)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                  <svg style={{ width: '1.25rem', height: '1.25rem', color: 'var(--color-success-600)' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  <span style={{ fontWeight: '600', color: 'var(--color-success-800)' }}>机会发现</span>
                </div>
                <p style={{ color: 'var(--color-success-700)', fontSize: '0.875rem' }}>
                  耳机市场增长强劲(+25.8%)，建议增加AirPods系列库存，预计未来7天需求将持续上升。
                </p>
              </div>
              
              <div style={{
                padding: '1rem',
                background: 'var(--color-warning-50)',
                border: '1px solid var(--color-warning-200)',
                borderRadius: 'var(--radius-lg)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                  <svg style={{ width: '1.25rem', height: '1.25rem', color: 'var(--color-warning-600)' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <span style={{ fontWeight: '600', color: 'var(--color-warning-800)' }}>风险提醒</span>
                </div>
                <p style={{ color: 'var(--color-warning-700)', fontSize: '0.875rem' }}>
                  笔记本市场出现下滑(-3.2%)，建议关注竞品动态，考虑调整MacBook Pro定价策略。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightsPage;
