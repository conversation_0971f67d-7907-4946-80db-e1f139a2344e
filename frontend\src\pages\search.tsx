import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';

const SearchPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  if (isLoading) {
    return <LoadingScreen message="加载搜索发现..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 模拟搜索结果数据
  const searchResults = [
    {
      id: 1,
      name: 'iPhone 15 Pro Max 1TB',
      brand: 'Apple',
      category: '手机',
      price: 12999,
      originalPrice: 13999,
      rating: 4.8,
      reviews: 1250,
      tags: ['5G', '钛金属', 'A17 Pro'],
      trending: true,
      discount: 7,
      source: '天猫旗舰店'
    },
    {
      id: 2,
      name: '小米14 Ultra 16GB+1TB',
      brand: '小米',
      category: '手机',
      price: 6999,
      originalPrice: 7499,
      rating: 4.6,
      reviews: 890,
      tags: ['徕卡影像', '骁龙8 Gen3', '无线充电'],
      trending: false,
      discount: 7,
      source: '小米官方店'
    },
    {
      id: 3,
      name: 'MacBook Pro 14英寸 M3 Pro',
      brand: 'Apple',
      category: '笔记本',
      price: 16999,
      originalPrice: 17999,
      rating: 4.9,
      reviews: 567,
      tags: ['M3 Pro芯片', 'Liquid视网膜', '18小时续航'],
      trending: true,
      discount: 6,
      source: 'Apple Store'
    }
  ];

  const categories = ['all', '手机', '笔记本', '平板', '耳机'];

  const filteredResults = searchResults.filter(product => {
    const matchesSearch = searchTerm === '' || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.brand.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <h1 style={{
              fontSize: '2rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              搜索发现
            </h1>
            <p style={{
              fontSize: '1rem',
              color: 'var(--color-gray-600)'
            }}>
              发现热门产品和最佳价格
            </p>
          </div>
        </div>

        {/* 搜索栏 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              gap: '1rem',
              alignItems: 'center',
              flexWrap: 'wrap'
            }}>
              <div style={{ flex: 1, minWidth: '300px', position: 'relative' }}>
                <svg style={{
                  position: 'absolute',
                  left: '0.75rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  width: '1.25rem',
                  height: '1.25rem',
                  color: 'var(--color-gray-400)'
                }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                </svg>
                <input
                  type="text"
                  className="input"
                  placeholder="搜索产品名称、品牌或型号..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{ paddingLeft: '2.5rem' }}
                />
              </div>
              
              <select
                className="input"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                style={{ minWidth: '120px' }}
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? '全部分类' : category}
                  </option>
                ))}
              </select>
              
              <button className="btn btn-secondary">
                <svg style={{ width: '1.25rem', height: '1.25rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z" />
                </svg>
                筛选
              </button>
            </div>
          </div>
        </div>

        {/* 搜索结果 */}
        <div className="card">
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: 'var(--color-gray-900)'
              }}>
                搜索结果 ({filteredResults.length})
              </h2>
            </div>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
              gap: '1.5rem'
            }}>
              {filteredResults.map((product) => (
                <div key={product.id} className="card">
                  <div className="card-body">
                    <div style={{
                      display: 'flex',
                      gap: '1rem'
                    }}>
                      <div style={{
                        width: '5rem',
                        height: '5rem',
                        background: 'var(--color-gray-200)',
                        borderRadius: 'var(--radius-lg)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.75rem',
                        color: 'var(--color-gray-500)',
                        flexShrink: 0
                      }}>
                        图片
                      </div>
                      
                      <div style={{ flex: 1 }}>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: '0.5rem'
                        }}>
                          <h3 style={{
                            fontSize: '1rem',
                            fontWeight: '600',
                            color: 'var(--color-gray-900)',
                            lineHeight: '1.4'
                          }}>
                            {product.name}
                          </h3>
                          {product.trending && (
                            <span className="badge badge-warning" style={{ fontSize: '0.625rem' }}>
                              热门
                            </span>
                          )}
                        </div>
                        
                        <p style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '0.5rem'
                        }}>
                          {product.brand} · {product.source}
                        </p>
                        
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          marginBottom: '0.75rem'
                        }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                            <svg style={{ width: '1rem', height: '1rem', color: 'var(--color-warning-500)' }} fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                            </svg>
                            <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                              {product.rating}
                            </span>
                          </div>
                          <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>
                            ({product.reviews}条评价)
                          </span>
                        </div>
                        
                        <div style={{
                          display: 'flex',
                          flexWrap: 'wrap',
                          gap: '0.25rem',
                          marginBottom: '0.75rem'
                        }}>
                          {product.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              style={{
                                fontSize: '0.625rem',
                                padding: '0.125rem 0.375rem',
                                background: 'var(--color-primary-100)',
                                color: 'var(--color-primary-700)',
                                borderRadius: 'var(--radius-sm)'
                              }}
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                        
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center'
                        }}>
                          <div>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                              <span style={{
                                fontSize: '1.25rem',
                                fontWeight: '700',
                                color: 'var(--color-error-600)'
                              }}>
                                ¥{product.price.toLocaleString()}
                              </span>
                              {product.discount > 0 && (
                                <span className="badge badge-error" style={{ fontSize: '0.625rem' }}>
                                  -{product.discount}%
                                </span>
                              )}
                            </div>
                            {product.originalPrice > product.price && (
                              <span style={{
                                fontSize: '0.75rem',
                                color: 'var(--color-gray-500)',
                                textDecoration: 'line-through'
                              }}>
                                ¥{product.originalPrice.toLocaleString()}
                              </span>
                            )}
                          </div>
                          
                          <div style={{ display: 'flex', gap: '0.5rem' }}>
                            <button className="btn btn-ghost btn-sm">
                              <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                              </svg>
                            </button>
                            <button className="btn btn-primary btn-sm">
                              <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                              </svg>
                              添加
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredResults.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <svg style={{
                  width: '3rem',
                  height: '3rem',
                  margin: '0 auto 1rem',
                  color: 'var(--color-gray-300)'
                }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                </svg>
                <p>没有找到匹配的产品</p>
                <p style={{ fontSize: '0.875rem', marginTop: '0.5rem' }}>
                  尝试调整搜索关键词或筛选条件
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
