import React, { ReactNode } from 'react';
import { useRouter } from 'next/router';
import { useAuth } from '@/contexts/AuthContext';
import Navbar from './Navbar';
import LoadingScreen from '@/components/LoadingScreen';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  // 不需要布局的页面
  const noLayoutPages = ['/auth/login', '/auth/register', '/auth/forgot-password'];
  const isNoLayoutPage = noLayoutPages.includes(router.pathname);

  // 如果是认证页面，直接渲染内容
  if (isNoLayoutPage) {
    return <>{children}</>;
  }

  // 如果正在加载认证状态，显示加载屏幕
  if (isLoading) {
    return <LoadingScreen />;
  }

  // 如果未认证，重定向到登录页面
  if (!isAuthenticated) {
    router.push('/auth/login');
    return <LoadingScreen />;
  }

  // 渲染主布局
  return (
    <div style={{
      minHeight: '100vh',
      background: 'var(--color-gray-50)'
    }}>
      {/* 水平导航栏 */}
      <Navbar />

      {/* 页面内容 */}
      <main style={{
        padding: '2rem 0'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 1rem'
        }}>
          {children}
        </div>
      </main>
    </div>
  );
};

export default Layout;
