/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/products"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/products\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/products.tsx */ \"./src/pages/products.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/products\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDYWktcHJvZHVjdC1zZWxlY3QlNUNmcm9udGVuZCU1Q3NyYyU1Q3BhZ2VzJTVDcHJvZHVjdHMudHN4JnBhZ2U9JTJGcHJvZHVjdHMhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMERBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz84NzAxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvcHJvZHVjdHNcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9wcm9kdWN0cy50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL3Byb2R1Y3RzXCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts!\n"));

/***/ }),

/***/ "./src/pages/products.tsx":
/*!********************************!*\
  !*** ./src/pages/products.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/LoadingScreen */ \"./src/components/LoadingScreen.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n// 使用内联SVG图标避免导入问题\nconst ProductsPage = ()=>{\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProduct, setNewProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        brand: \"\",\n        category: \"\",\n        price: \"\",\n        stock: \"\"\n    });\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingScreen__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            message: \"加载产品管理...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n            lineNumber: 20,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (!isAuthenticated) {\n        return null;\n    }\n    // 模拟产品数据\n    const products = [\n        {\n            id: 1,\n            name: \"iPhone 15 Pro Max\",\n            brand: \"Apple\",\n            category: \"手机\",\n            price: 9999,\n            stock: 50,\n            status: \"在售\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-15\"\n        },\n        {\n            id: 2,\n            name: \"小米14 Ultra\",\n            brand: \"小米\",\n            category: \"手机\",\n            price: 5999,\n            stock: 30,\n            status: \"在售\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-10\"\n        },\n        {\n            id: 3,\n            name: \"MacBook Pro M3\",\n            brand: \"Apple\",\n            category: \"笔记本\",\n            price: 14999,\n            stock: 15,\n            status: \"在售\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-08\"\n        },\n        {\n            id: 4,\n            name: \"AirPods Pro 2\",\n            brand: \"Apple\",\n            category: \"耳机\",\n            price: 1899,\n            stock: 0,\n            status: \"缺货\",\n            image: \"/api/placeholder/100/100\",\n            createdAt: \"2024-01-05\"\n        }\n    ];\n    const categories = [\n        \"all\",\n        \"手机\",\n        \"笔记本\",\n        \"平板\",\n        \"耳机\"\n    ];\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.brand.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === \"all\" || product.category === selectedCategory;\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)\",\n            padding: \"2rem 0\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: \"1200px\",\n                    margin: \"0 auto\",\n                    padding: \"0 1rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        style: {\n                            marginBottom: \"2rem\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                style: {\n                                                    fontSize: \"2rem\",\n                                                    fontWeight: \"700\",\n                                                    color: \"var(--color-gray-900)\",\n                                                    marginBottom: \"0.5rem\"\n                                                },\n                                                children: \"产品管理\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    fontSize: \"1rem\",\n                                                    color: \"var(--color-gray-600)\"\n                                                },\n                                                children: \"管理您的产品库存和信息\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-primary\",\n                                        onClick: ()=>setShowAddModal(true),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    width: \"1.25rem\",\n                                                    height: \"1.25rem\"\n                                                },\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"添加产品\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        style: {\n                            marginBottom: \"2rem\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"1rem\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            flex: 1,\n                                            position: \"relative\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    left: \"0.75rem\",\n                                                    top: \"50%\",\n                                                    transform: \"translateY(-50%)\",\n                                                    width: \"1.25rem\",\n                                                    height: \"1.25rem\",\n                                                    color: \"var(--color-gray-400)\"\n                                                },\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                className: \"input\",\n                                                placeholder: \"搜索产品名称或品牌...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                style: {\n                                                    paddingLeft: \"2.5rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    width: \"1.25rem\",\n                                                    height: \"1.25rem\"\n                                                },\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"筛选\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        style: {\n                            marginBottom: \"2rem\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    gap: \"0.5rem\",\n                                    flexWrap: \"wrap\"\n                                },\n                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn \".concat(selectedCategory === category ? \"btn-primary\" : \"btn-ghost\", \" btn-sm\"),\n                                        onClick: ()=>setSelectedCategory(category),\n                                        children: category === \"all\" ? \"全部\" : category\n                                    }, category, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"card\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"grid\",\n                                        gridTemplateColumns: \"repeat(auto-fill, minmax(300px, 1fr))\",\n                                        gap: \"1.5rem\"\n                                    },\n                                    children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"card\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"card-body\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: \"flex\",\n                                                        alignItems: \"flex-start\",\n                                                        gap: \"1rem\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                width: \"4rem\",\n                                                                height: \"4rem\",\n                                                                background: \"var(--color-gray-200)\",\n                                                                borderRadius: \"var(--radius-lg)\",\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                justifyContent: \"center\",\n                                                                fontSize: \"0.75rem\",\n                                                                color: \"var(--color-gray-500)\"\n                                                            },\n                                                            children: \"图片\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                flex: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    style: {\n                                                                        fontSize: \"1.125rem\",\n                                                                        fontWeight: \"600\",\n                                                                        color: \"var(--color-gray-900)\",\n                                                                        marginBottom: \"0.25rem\"\n                                                                    },\n                                                                    children: product.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    style: {\n                                                                        fontSize: \"0.875rem\",\n                                                                        color: \"var(--color-gray-600)\",\n                                                                        marginBottom: \"0.5rem\"\n                                                                    },\n                                                                    children: [\n                                                                        product.brand,\n                                                                        \" \\xb7 \",\n                                                                        product.category\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        justifyContent: \"space-between\",\n                                                                        alignItems: \"center\",\n                                                                        marginBottom: \"1rem\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontSize: \"1.25rem\",\n                                                                                fontWeight: \"700\",\n                                                                                color: \"var(--color-primary-600)\"\n                                                                            },\n                                                                            children: [\n                                                                                \"\\xa5\",\n                                                                                product.price.toLocaleString()\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                            lineNumber: 242,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"badge \".concat(product.status === \"在售\" ? \"badge-success\" : \"badge-error\"),\n                                                                            children: product.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: \"0.875rem\",\n                                                                        color: \"var(--color-gray-600)\",\n                                                                        marginBottom: \"1rem\"\n                                                                    },\n                                                                    children: [\n                                                                        \"库存: \",\n                                                                        product.stock,\n                                                                        \" 件\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                    lineNumber: 255,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: \"flex\",\n                                                                        gap: \"0.5rem\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"btn btn-ghost btn-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    style: {\n                                                                                        width: \"1rem\",\n                                                                                        height: \"1rem\"\n                                                                                    },\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 2,\n                                                                                            d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                            lineNumber: 269,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                            strokeLinecap: \"round\",\n                                                                                            strokeLinejoin: \"round\",\n                                                                                            strokeWidth: 2,\n                                                                                            d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                            lineNumber: 270,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                    lineNumber: 268,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                \"查看\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"btn btn-ghost btn-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    style: {\n                                                                                        width: \"1rem\",\n                                                                                        height: \"1rem\"\n                                                                                    },\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                        lineNumber: 276,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                    lineNumber: 275,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                \"编辑\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            className: \"btn btn-ghost btn-sm\",\n                                                                            style: {\n                                                                                color: \"var(--color-error-600)\"\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                                    style: {\n                                                                                        width: \"1rem\",\n                                                                                        height: \"1rem\"\n                                                                                    },\n                                                                                    fill: \"none\",\n                                                                                    viewBox: \"0 0 24 24\",\n                                                                                    stroke: \"currentColor\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                        strokeLinecap: \"round\",\n                                                                                        strokeLinejoin: \"round\",\n                                                                                        strokeWidth: 2,\n                                                                                        d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                        lineNumber: 282,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                                    lineNumber: 281,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                \"删除\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, product.id, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, undefined),\n                                filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: \"center\",\n                                        padding: \"3rem\",\n                                        color: \"var(--color-gray-500)\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"没有找到匹配的产品\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            showAddModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: \"center\",\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card\",\n                    style: {\n                        width: \"100%\",\n                        maxWidth: \"500px\",\n                        margin: \"1rem\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            fontSize: \"1.25rem\",\n                                            fontWeight: \"600\",\n                                            margin: 0\n                                        },\n                                        children: \"添加新产品\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-ghost btn-sm\",\n                                        onClick: ()=>setShowAddModal(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                width: \"1.25rem\",\n                                                height: \"1.25rem\"\n                                            },\n                                            fill: \"none\",\n                                            viewBox: \"0 0 24 24\",\n                                            stroke: \"currentColor\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M6 18L18 6M6 6l12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"card-body\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: (e)=>{\n                                    e.preventDefault();\n                                    // 这里可以添加保存逻辑\n                                    alert(\"产品添加功能演示：\" + JSON.stringify(newProduct, null, 2));\n                                    setShowAddModal(false);\n                                    setNewProduct({\n                                        name: \"\",\n                                        brand: \"\",\n                                        category: \"\",\n                                        price: \"\",\n                                        stock: \"\"\n                                    });\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            flexDirection: \"column\",\n                                            gap: \"1rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            fontSize: \"0.875rem\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"0.5rem\"\n                                                        },\n                                                        children: \"产品名称 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        className: \"input\",\n                                                        value: newProduct.name,\n                                                        onChange: (e)=>setNewProduct({\n                                                                ...newProduct,\n                                                                name: e.target.value\n                                                            }),\n                                                        placeholder: \"请输入产品名称\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            fontSize: \"0.875rem\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"0.5rem\"\n                                                        },\n                                                        children: \"品牌 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        className: \"input\",\n                                                        value: newProduct.brand,\n                                                        onChange: (e)=>setNewProduct({\n                                                                ...newProduct,\n                                                                brand: e.target.value\n                                                            }),\n                                                        placeholder: \"请输入品牌名称\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            fontSize: \"0.875rem\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"0.5rem\"\n                                                        },\n                                                        children: \"分类 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"input\",\n                                                        value: newProduct.category,\n                                                        onChange: (e)=>setNewProduct({\n                                                                ...newProduct,\n                                                                category: e.target.value\n                                                            }),\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"请选择分类\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"手机\",\n                                                                children: \"手机\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"笔记本\",\n                                                                children: \"笔记本\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"平板\",\n                                                                children: \"平板\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"耳机\",\n                                                                children: \"耳机\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            fontSize: \"0.875rem\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"0.5rem\"\n                                                        },\n                                                        children: \"价格 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        className: \"input\",\n                                                        value: newProduct.price,\n                                                        onChange: (e)=>setNewProduct({\n                                                                ...newProduct,\n                                                                price: e.target.value\n                                                            }),\n                                                        placeholder: \"请输入价格\",\n                                                        min: \"0\",\n                                                        step: \"0.01\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: \"block\",\n                                                            fontSize: \"0.875rem\",\n                                                            fontWeight: \"500\",\n                                                            marginBottom: \"0.5rem\"\n                                                        },\n                                                        children: \"库存数量 *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        className: \"input\",\n                                                        value: newProduct.stock,\n                                                        onChange: (e)=>setNewProduct({\n                                                                ...newProduct,\n                                                                stock: e.target.value\n                                                            }),\n                                                        placeholder: \"请输入库存数量\",\n                                                        min: \"0\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: \"flex\",\n                                            gap: \"1rem\",\n                                            marginTop: \"2rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"btn btn-ghost\",\n                                                onClick: ()=>setShowAddModal(false),\n                                                style: {\n                                                    flex: 1\n                                                },\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"btn btn-primary\",\n                                                style: {\n                                                    flex: 1\n                                                },\n                                                children: \"添加产品\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                        lineNumber: 424,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                            lineNumber: 337,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\ai-product-select\\\\frontend\\\\src\\\\pages\\\\products.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProductsPage, \"iWb+jnbItlaNtb27KbqTJ8zd8fM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth\n    ];\n});\n_c = ProductsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ProductsPage);\nvar _c;\n$RefreshReg$(_c, \"ProductsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/products.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cai-product-select%5Cfrontend%5Csrc%5Cpages%5Cproducts.tsx&page=%2Fproducts!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);