import React from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';
import {
  ChartBarIcon,
  ShoppingBagIcon,
  CloudArrowUpIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

const DashboardPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();

  if (isLoading) {
    return <LoadingScreen message="加载仪表板..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  const stats = [
    {
      name: '总产品数',
      value: '12',
      icon: ShoppingBagIcon,
      change: '+4.75%',
      changeType: 'positive' as const,
    },
    {
      name: '分析报告',
      value: '8',
      icon: ChartBarIcon,
      change: '+54.02%',
      changeType: 'positive' as const,
    },
    {
      name: '导入任务',
      value: '3',
      icon: CloudArrowUpIcon,
      change: '-1.39%',
      changeType: 'negative' as const,
    },
    {
      name: '活跃用户',
      value: '2',
      icon: UserGroupIcon,
      change: '+10.18%',
      changeType: 'positive' as const,
    },
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '2rem 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <h1 style={{
              fontSize: '2rem',
              fontWeight: '700',
              color: 'var(--color-gray-900)',
              marginBottom: '0.5rem'
            }}>
              仪表板
            </h1>
            <p style={{
              fontSize: '1rem',
              color: 'var(--color-gray-600)'
            }}>
              欢迎回来！这里是您的数据概览。
            </p>
          </div>
        </div>

        {/* 统计卡片 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          {stats.map((stat) => (
            <div key={stat.name} className="card">
              <div className="card-body" style={{ position: 'relative' }}>
                <div style={{
                  position: 'absolute',
                  top: '1.5rem',
                  right: '1.5rem',
                  width: '2.5rem',
                  height: '2.5rem',
                  background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: 'var(--shadow-md)'
                }}>
                  <stat.icon style={{ width: '1.25rem', height: '1.25rem', color: 'white' }} />
                </div>
                
                <div style={{ paddingRight: '4rem' }}>
                  <p style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: 'var(--color-gray-600)',
                    marginBottom: '0.5rem'
                  }}>
                    {stat.name}
                  </p>
                  
                  <div style={{ display: 'flex', alignItems: 'baseline', gap: '0.5rem' }}>
                    <p style={{
                      fontSize: '2rem',
                      fontWeight: '700',
                      color: 'var(--color-gray-900)'
                    }}>
                      {stat.value}
                    </p>
                    
                    <span className={`badge ${stat.changeType === 'positive' ? 'badge-success' : 'badge-error'}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 快速操作 */}
        <div className="card">
          <div className="card-header">
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: 'var(--color-gray-900)',
              margin: 0
            }}>
              快速操作
            </h2>
          </div>
          
          <div className="card-body">
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '1rem'
            }}>
              <button className="btn btn-primary" style={{ height: 'auto', padding: '1rem' }}>
                <div style={{ textAlign: 'center' }}>
                  <CloudArrowUpIcon style={{ width: '1.5rem', height: '1.5rem', marginBottom: '0.5rem' }} />
                  <div style={{ fontWeight: '600' }}>导入数据</div>
                  <div style={{ fontSize: '0.75rem', opacity: 0.8 }}>上传Excel或CSV文件</div>
                </div>
              </button>

              <button className="btn btn-secondary" style={{ height: 'auto', padding: '1rem' }}>
                <div style={{ textAlign: 'center' }}>
                  <ChartBarIcon style={{ width: '1.5rem', height: '1.5rem', marginBottom: '0.5rem' }} />
                  <div style={{ fontWeight: '600' }}>AI分析</div>
                  <div style={{ fontSize: '0.75rem', opacity: 0.8 }}>智能分析产品数据</div>
                </div>
              </button>

              <button className="btn btn-ghost" style={{ height: 'auto', padding: '1rem' }}>
                <div style={{ textAlign: 'center' }}>
                  <ShoppingBagIcon style={{ width: '1.5rem', height: '1.5rem', marginBottom: '0.5rem' }} />
                  <div style={{ fontWeight: '600' }}>产品管理</div>
                  <div style={{ fontSize: '0.75rem', opacity: 0.8 }}>管理产品库存</div>
                </div>
              </button>
            </div>
          </div>
        </div>

        {/* 底部信息 */}
        <div style={{
          textAlign: 'center',
          marginTop: '2rem',
          padding: '1rem',
          color: 'var(--color-gray-600)',
          fontSize: '0.875rem'
        }}>
          <p>SmartPick 智能选品分析平台 - 让数据驱动您的选品决策</p>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
