import React, { useState } from 'react';
import { useRequireAuth } from '@/contexts/AuthContext';
import LoadingScreen from '@/components/LoadingScreen';

const AnalysisPage: React.FC = () => {
  const { isAuthenticated, isLoading } = useRequireAuth();
  const [selectedCategory, setSelectedCategory] = useState('all');

  if (isLoading) {
    return <LoadingScreen message="加载智能分析..." />;
  }

  if (!isAuthenticated) {
    return null;
  }

  // 模拟分析报告数据
  const reports = [
    {
      id: 1,
      title: 'iPhone 15 Pro Max 市场分析报告',
      category: '手机',
      status: '已完成',
      trend: 'up',
      score: 85,
      createdAt: '2024-01-15',
      summary: '该产品在高端手机市场表现优异，建议加大库存投入',
      metrics: {
        marketShare: '15.2%',
        profitMargin: '32%',
        competitorCount: 8
      }
    },
    {
      id: 2,
      title: '小米14 Ultra 竞品分析',
      category: '手机',
      status: '已完成',
      trend: 'up',
      score: 78,
      createdAt: '2024-01-12',
      summary: '性价比优势明显，在中高端市场有较强竞争力',
      metrics: {
        marketShare: '12.8%',
        profitMargin: '28%',
        competitorCount: 12
      }
    },
    {
      id: 3,
      title: 'MacBook Pro M3 销售预测',
      category: '笔记本',
      status: '进行中',
      trend: 'up',
      score: 92,
      createdAt: '2024-01-10',
      summary: '预计Q2销量将增长25%，建议提前备货',
      metrics: {
        marketShare: '8.5%',
        profitMargin: '35%',
        competitorCount: 6
      }
    }
  ];

  const categories = ['all', '手机', '笔记本', '耳机'];
  const filteredReports = selectedCategory === 'all' 
    ? reports 
    : reports.filter(report => report.category === selectedCategory);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      padding: '2rem 0'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '0 1rem'
      }}>
        {/* 页面标题 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: '700',
                  color: 'var(--color-gray-900)',
                  marginBottom: '0.5rem'
                }}>
                  智能分析
                </h1>
                <p style={{
                  fontSize: '1rem',
                  color: 'var(--color-gray-600)'
                }}>
                  AI驱动的产品市场分析和预测
                </p>
              </div>
              <button className="btn btn-primary">
                <svg style={{ width: '1.25rem', height: '1.25rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                </svg>
                新建分析
              </button>
            </div>
          </div>
        </div>

        {/* 统计概览 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '1.5rem',
          marginBottom: '2rem'
        }}>
          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    {reports.length}
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    分析报告
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-success-500), var(--color-success-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    82%
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    平均准确率
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-body">
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  background: 'linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600))',
                  borderRadius: 'var(--radius-lg)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <svg style={{ width: '1.5rem', height: '1.5rem', color: 'white' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09Z" />
                  </svg>
                </div>
                <div>
                  <p style={{ fontSize: '2rem', fontWeight: '700', color: 'var(--color-gray-900)' }}>
                    15
                  </p>
                  <p style={{ fontSize: '0.875rem', color: 'var(--color-gray-600)' }}>
                    AI模型
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 分类筛选 */}
        <div className="card" style={{ marginBottom: '2rem' }}>
          <div className="card-body">
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              {categories.map((category) => (
                <button
                  key={category}
                  className={`btn ${selectedCategory === category ? 'btn-primary' : 'btn-ghost'} btn-sm`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category === 'all' ? '全部' : category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 分析报告列表 */}
        <div className="card">
          <div className="card-body">
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {filteredReports.map((report) => (
                <div key={report.id} className="card">
                  <div className="card-body">
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                      marginBottom: '1rem'
                    }}>
                      <div style={{ flex: 1 }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '0.5rem' }}>
                          <h3 style={{
                            fontSize: '1.25rem',
                            fontWeight: '600',
                            color: 'var(--color-gray-900)'
                          }}>
                            {report.title}
                          </h3>
                          <span className={`badge ${report.status === '已完成' ? 'badge-success' : 'badge-warning'}`}>
                            {report.status}
                          </span>
                          <svg style={{ 
                            width: '1.25rem', 
                            height: '1.25rem', 
                            color: report.trend === 'up' ? 'var(--color-success-500)' : 'var(--color-error-500)' 
                          }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                          </svg>
                        </div>
                        
                        <p style={{
                          fontSize: '0.875rem',
                          color: 'var(--color-gray-600)',
                          marginBottom: '1rem'
                        }}>
                          {report.summary}
                        </p>
                        
                        <div style={{
                          display: 'grid',
                          gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                          gap: '1rem',
                          marginBottom: '1rem'
                        }}>
                          <div>
                            <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>市场份额</span>
                            <p style={{ fontSize: '1rem', fontWeight: '600', color: 'var(--color-gray-900)' }}>
                              {report.metrics.marketShare}
                            </p>
                          </div>
                          <div>
                            <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>利润率</span>
                            <p style={{ fontSize: '1rem', fontWeight: '600', color: 'var(--color-gray-900)' }}>
                              {report.metrics.profitMargin}
                            </p>
                          </div>
                          <div>
                            <span style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>竞品数量</span>
                            <p style={{ fontSize: '1rem', fontWeight: '600', color: 'var(--color-gray-900)' }}>
                              {report.metrics.competitorCount}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem'
                      }}>
                        <div style={{ textAlign: 'center' }}>
                          <div style={{
                            fontSize: '1.5rem',
                            fontWeight: '700',
                            color: report.score >= 80 ? 'var(--color-success-600)' : 
                                   report.score >= 60 ? 'var(--color-warning-600)' : 'var(--color-error-600)'
                          }}>
                            {report.score}
                          </div>
                          <div style={{ fontSize: '0.75rem', color: 'var(--color-gray-500)' }}>
                            分析评分
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span style={{ fontSize: '0.875rem', color: 'var(--color-gray-500)' }}>
                        创建时间: {report.createdAt}
                      </span>
                      
                      <div style={{ display: 'flex', gap: '0.5rem' }}>
                        <button className="btn btn-ghost btn-sm">
                          <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          查看详情
                        </button>
                        <button className="btn btn-ghost btn-sm">
                          <svg style={{ width: '1rem', height: '1rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          导出报告
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {filteredReports.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '3rem',
                color: 'var(--color-gray-500)'
              }}>
                <p>暂无分析报告</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalysisPage;
