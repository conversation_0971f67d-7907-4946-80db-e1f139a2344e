import React from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import { 
  HomeIcon, 
  CubeIcon, 
  ChartBarIcon, 
  SparklesIcon,
  CloudArrowDownIcon,
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline';

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
}

const Navbar: React.FC = () => {
  const router = useRouter();

  // 导航菜单项
  const navigation: NavItem[] = [
    {
      name: '仪表板',
      href: '/dashboard',
      icon: HomeIcon,
    },
    {
      name: '产品管理',
      href: '/products',
      icon: CubeIcon,
    },
    {
      name: '智能分析',
      href: '/analysis',
      icon: SparklesIcon,
    },
    {
      name: '数据导入',
      href: '/import',
      icon: CloudArrowDownIcon,
    },
    {
      name: '市场洞察',
      href: '/insights',
      icon: ChartBarIcon,
    },
    {
      name: '搜索发现',
      href: '/search',
      icon: MagnifyingGlassIcon,
    },
  ];

  const isCurrentPage = (href: string) => {
    return router.pathname === href;
  };

  return (
    <nav style={{
      background: 'white',
      borderBottom: '1px solid var(--color-gray-200)',
      padding: '0 2rem',
      boxShadow: 'var(--shadow-sm)'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: '4rem',
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Logo */}
        <Link href="/dashboard" passHref>
          <div className="link flex items-center" style={{ cursor: 'pointer' }}>
            <div className="flex-shrink-0">
              <div className="logo-container">
                <SparklesIcon className="logo-icon" />
              </div>
            </div>
            <div className="ml-3">
              <h1 className="text-xl font-bold text-gray-900">
                SmartPick
              </h1>
            </div>
          </div>
        </Link>

        {/* 导航菜单 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem'
        }}>
          {navigation.map((item) => {
            const Icon = item.icon;
            const isCurrent = isCurrentPage(item.href);
            
            return (
              <Link key={item.name} href={item.href} passHref>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    padding: '0.75rem 1rem',
                    borderRadius: 'var(--radius-lg)',
                    textDecoration: 'none',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    transition: 'all var(--transition-fast)',
                    color: isCurrent ? 'var(--color-primary-700)' : 'var(--color-gray-600)',
                    background: isCurrent ? 'var(--color-primary-100)' : 'transparent',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    if (!isCurrent) {
                      e.currentTarget.style.background = 'var(--color-gray-100)';
                      e.currentTarget.style.color = 'var(--color-gray-900)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isCurrent) {
                      e.currentTarget.style.background = 'transparent';
                      e.currentTarget.style.color = 'var(--color-gray-600)';
                    }
                  }}
                >
                  <Icon style={{
                    width: '1.25rem',
                    height: '1.25rem',
                    color: isCurrent ? 'var(--color-primary-500)' : 'var(--color-gray-400)'
                  }} />
                  {item.name}
                </div>
              </Link>
            );
          })}
        </div>

        {/* 右侧操作区 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '1rem'
        }}>
          {/* 搜索按钮 */}
          <button className="header-button">
            <MagnifyingGlassIcon className="header-icon" />
          </button>
          
          {/* 通知按钮 */}
          <button className="header-button" style={{ position: 'relative' }}>
            <svg style={{ width: '1.25rem', height: '1.25rem' }} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 0-6 6v2.25a2.25 2.25 0 0 1-2.25 2.25H2a.75.75 0 0 0 0 1.5h16.5a.75.75 0 0 0 0-1.5h-.25a2.25 2.25 0 0 1-2.25-2.25V9.75a6 6 0 0 0-6-6z" />
            </svg>
            <span style={{
              position: 'absolute',
              top: '0.25rem',
              right: '0.25rem',
              width: '0.5rem',
              height: '0.5rem',
              background: 'var(--color-error-500)',
              borderRadius: '50%'
            }}></span>
          </button>
          
          {/* 用户头像 */}
          <button className="header-button">
            <div style={{
              width: '2rem',
              height: '2rem',
              background: 'var(--color-gray-300)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>U</span>
            </div>
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
