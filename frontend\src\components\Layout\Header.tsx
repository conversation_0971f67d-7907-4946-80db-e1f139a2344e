import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import {
  MagnifyingGlassIcon,
  BellIcon,
  UserIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useNotifications } from '@/contexts/NotificationContext';
import Dropdown from '@/components/UI/Dropdown';
import SearchModal from '@/components/SearchModal';

const Header: React.FC = () => {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { theme, setTheme, toggleTheme } = useTheme();
  const { notifications, unreadCount, markAsRead } = useNotifications();
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  // 主题选项
  const themeOptions = [
    { value: 'light', label: '浅色', icon: SunIcon },
    { value: 'dark', label: '深色', icon: MoonIcon },
    { value: 'auto', label: '跟随系统', icon: ComputerDesktopIcon },
  ];

  // 用户菜单选项
  const userMenuItems = [
    {
      label: '个人资料',
      href: '/profile',
      icon: UserIcon,
    },
    {
      label: '设置',
      href: '/settings',
      icon: Cog6ToothIcon,
    },
    {
      type: 'divider' as const,
    },
    {
      label: '退出登录',
      onClick: logout,
      icon: ArrowRightOnRectangleIcon,
      className: 'text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300',
    },
  ];

  // 获取页面标题
  const getPageTitle = () => {
    const titles: Record<string, string> = {
      '/dashboard': '仪表板',
      '/products': '产品管理',
      '/analysis': '智能分析',
      '/crawler': '数据爬取',
      '/insights': '市场洞察',
      '/search': '搜索发现',
      '/notifications': '通知中心',
      '/profile': '个人资料',
      '/settings': '设置',
    };

    return titles[router.pathname] || '智能选品系统';
  };

  // 处理搜索
  const handleSearch = (query: string) => {
    router.push(`/search?q=${encodeURIComponent(query)}`);
    setIsSearchOpen(false);
  };

  return (
    <>
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* 页面标题 */}
            <div className="flex items-center">
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
                {getPageTitle()}
              </h1>
            </div>

            {/* 右侧操作区 */}
            <div className="flex items-center space-x-4">
              {/* 搜索按钮 */}
              <button
                type="button"
                className="p-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors duration-200"
                onClick={() => setIsSearchOpen(true)}
              >
                <MagnifyingGlassIcon className="h-5 w-5" />
              </button>

              {/* 主题切换 */}
              <Dropdown
                trigger={
                  <button
                    type="button"
                    className="p-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors duration-200"
                  >
                    {theme === 'light' && <SunIcon className="h-5 w-5" />}
                    {theme === 'dark' && <MoonIcon className="h-5 w-5" />}
                    {theme === 'auto' && <ComputerDesktopIcon className="h-5 w-5" />}
                  </button>
                }
                items={themeOptions.map(option => ({
                  label: option.label,
                  icon: option.icon,
                  onClick: () => setTheme(option.value as any),
                  active: theme === option.value,
                }))}
                align="right"
              />

              {/* 通知 */}
              <Dropdown
                trigger={
                  <button
                    type="button"
                    className="relative p-2 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors duration-200"
                  >
                    <BellIcon className="h-5 w-5" />
                    {unreadCount > 0 && (
                      <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                        {unreadCount > 99 ? '99+' : unreadCount}
                      </span>
                    )}
                  </button>
                }
                items={[
                  {
                    type: 'header',
                    label: `通知 (${unreadCount} 条未读)`,
                  },
                  ...notifications.slice(0, 5).map(notification => ({
                    label: notification.title,
                    description: notification.message,
                    timestamp: notification.timestamp,
                    unread: !notification.read,
                    onClick: () => {
                      markAsRead(notification.id);
                      if (notification.action) {
                        router.push(notification.action.url);
                      }
                    },
                  })),
                  {
                    type: 'divider' as const,
                  },
                  {
                    label: '查看全部通知',
                    href: '/notifications',
                  },
                ]}
                align="right"
                className="w-80"
              />

              {/* 用户菜单 */}
              <Dropdown
                trigger={
                  <button
                    type="button"
                    className="flex items-center p-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                      {user?.avatar_url ? (
                        <img
                          src={user.avatar_url}
                          alt={user?.username}
                          className="w-8 h-8 rounded-full"
                        />
                      ) : (
                        <UserIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                      )}
                    </div>
                  </button>
                }
                items={[
                  {
                    type: 'header',
                    label: user?.username || '用户',
                    description: user?.email,
                  },
                  {
                    type: 'divider' as const,
                  },
                  ...userMenuItems,
                ]}
                align="right"
              />
            </div>
          </div>
        </div>
      </header>

      {/* 搜索模态框 */}
      <SearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
        onSearch={handleSearch}
      />
    </>
  );
};

export default Header;
