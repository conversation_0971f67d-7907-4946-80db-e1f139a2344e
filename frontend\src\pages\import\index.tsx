import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/router';
import { useDropzone } from 'react-dropzone';
import {
  CloudArrowUpIcon,
  DocumentArrowDownIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { useRequireAuth } from '@/contexts/AuthContext';
import { useNotify } from '@/contexts/NotificationContext';
import apiService from '@/services/api';
import { LoadingSpinner } from '@/components/LoadingScreen';

interface ImportTask {
  id: string;
  title: string;
  file_name: string;
  file_size: number;
  file_type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  success_rows: number;
  failed_rows: number;
  total_rows: number;
  created_at: string;
  completed_at?: string;
  error_message?: string;
}

const ImportPage: React.FC = () => {
  useRequireAuth();
  const router = useRouter();
  const notify = useNotify();
  
  const [isUploading, setIsUploading] = useState(false);
  const [recentTasks, setRecentTasks] = useState<ImportTask[]>([]);
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);

  // 文件拖拽上传
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    
    const file = acceptedFiles[0];
    
    // 验证文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv'
    ];
    
    if (!allowedTypes.includes(file.type)) {
      notify.error('文件格式错误', '请上传Excel或CSV格式的文件');
      return;
    }
    
    // 验证文件大小（50MB）
    if (file.size > 50 * 1024 * 1024) {
      notify.error('文件过大', '文件大小不能超过50MB');
      return;
    }
    
    await uploadFile(file);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  // 上传文件
  const uploadFile = async (file: File) => {
    setIsUploading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // 使用fetch通过Next.js代理上传文件
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/v1/import/upload', {
        method: 'POST',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('上传失败');
      }

      const result = await response.json();
      
      notify.success('上传成功', '文件已上传，正在处理导入任务');
      
      // 跳转到任务详情页
      router.push(`/import/tasks/${result.data.importTaskId}`);
      
    } catch (error: any) {
      notify.error('上传失败', error.message || '文件上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };

  // 下载模板
  const downloadTemplate = async (type: 'excel' | 'csv') => {
    try {
      const endpoint = type === 'excel' ? '/import/template/excel' : '/import/template/csv';
      
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/v1${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
        },
      });
      
      if (!response.ok) {
        throw new Error('下载失败');
      }
      
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `产品数据导入模板.${type === 'excel' ? 'xlsx' : 'csv'}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      notify.success('下载成功', '模板文件已下载');
    } catch (error) {
      notify.error('下载失败', '模板下载失败，请重试');
    }
  };

  // 获取最近的导入任务
  const loadRecentTasks = async () => {
    setIsLoadingTasks(true);
    try {
      const response = await apiService.get('/import/tasks?limit=5');
      setRecentTasks(response.tasks || []);
    } catch (error) {
      console.error('获取导入任务失败:', error);
    } finally {
      setIsLoadingTasks(false);
    }
  };

  React.useEffect(() => {
    loadRecentTasks();
  }, []);

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircleIcon className="w-5 h-5 text-red-500" />;
      case 'processing':
        return <LoadingSpinner size="sm" className="text-blue-500" />;
      default:
        return <ClockIcon className="w-5 h-5 text-yellow-500" />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    const statusMap = {
      pending: '等待处理',
      processing: '处理中',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">数据导入</h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          上传Excel或CSV文件批量导入产品数据
        </p>
      </div>

      {/* 模板下载区域 */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            第一步：下载数据模板
          </h2>
        </div>
        <div className="card-body">
          <div className="flex items-start space-x-4">
            <InformationCircleIcon className="w-5 h-5 text-blue-500 flex-shrink-0 mt-1" />
            <div className="flex-1">
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                请先下载标准数据模板，按照模板格式填写产品信息后再上传。模板包含所有必填字段和可选字段的说明。
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => downloadTemplate('excel')}
                  className="btn btn-primary"
                >
                  <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
                  下载Excel模板
                </button>
                <button
                  onClick={() => downloadTemplate('csv')}
                  className="btn btn-secondary"
                >
                  <DocumentArrowDownIcon className="w-4 h-4 mr-2" />
                  下载CSV模板
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 文件上传区域 */}
      <div className="card">
        <div className="card-header">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            第二步：上传数据文件
          </h2>
        </div>
        <div className="card-body">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-primary-400 bg-primary-50 dark:bg-primary-900/20' 
                : 'border-gray-300 dark:border-gray-600 hover:border-primary-400 hover:bg-gray-50 dark:hover:bg-gray-800'
              }
              ${isUploading ? 'pointer-events-none opacity-50' : ''}
            `}
          >
            <input {...getInputProps()} />
            
            {isUploading ? (
              <div className="space-y-4">
                <LoadingSpinner size="lg" className="mx-auto" />
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  正在上传文件...
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <CloudArrowUpIcon className="w-6 h-6 text-gray-400 mx-auto" />
                <div>
                  <p className="text-lg font-medium text-gray-900 dark:text-white">
                    {isDragActive ? '释放文件以上传' : '拖拽文件到此处或点击选择'}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    支持Excel (.xlsx, .xls) 和CSV (.csv) 格式，最大50MB
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 最近的导入任务 */}
      <div className="card">
        <div className="card-header flex justify-between items-center">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            最近的导入任务
          </h2>
          <button
            onClick={() => router.push('/import/tasks')}
            className="text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400"
          >
            查看全部
          </button>
        </div>
        <div className="card-body">
          {isLoadingTasks ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : recentTasks.length > 0 ? (
            <div className="space-y-3">
              {recentTasks.map((task) => (
                <div
                  key={task.id}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                  onClick={() => router.push(`/import/tasks/${task.id}`)}
                >
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(task.status)}
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {task.file_name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {getStatusText(task.status)} • {new Date(task.created_at).toLocaleString('zh-CN')}
                      </p>
                    </div>
                  </div>
                  {task.status === 'completed' && (
                    <div className="text-right">
                      <p className="text-sm text-green-600 dark:text-green-400">
                        成功 {task.success_rows} 条
                      </p>
                      {task.failed_rows > 0 && (
                        <p className="text-xs text-red-500">
                          失败 {task.failed_rows} 条
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CloudArrowUpIcon className="w-8 h-8 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                暂无导入任务
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImportPage;
